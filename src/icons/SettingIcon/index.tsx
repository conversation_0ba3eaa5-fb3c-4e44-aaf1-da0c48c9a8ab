import * as React from "react"
import Svg, { SvgProps, Path } from "react-native-svg"
const SettingIcon = (props: SvgProps) => (
  <Svg
    width={props.width || 32}
    height={props.height || 32}
    viewBox="0 0 32 32"
    fill="none"
    {...props}
  >
    <Path
      stroke="#F8F8F8"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M16.5 20a4 4 0 1 0 0-8 4 4 0 0 0 0 8Z"
    />
    <Path
      stroke="#F8F8F8"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M26.367 20a2.2 2.2 0 0 0 .44 2.427l.08.08a2.668 2.668 0 1 1-3.774 3.773l-.08-.08a2.202 2.202 0 0 0-2.426-.44 2.2 2.2 0 0 0-1.334 2.013V28a2.667 2.667 0 0 1-5.333 0v-.12a2.2 2.2 0 0 0-1.44-2.013 2.2 2.2 0 0 0-2.427.44l-.08.08a2.667 2.667 0 1 1-3.773-3.774l.08-.08a2.2 2.2 0 0 0 .44-2.426 2.2 2.2 0 0 0-2.013-1.334H4.5a2.667 2.667 0 0 1 0-5.333h.12A2.2 2.2 0 0 0 6.633 12a2.2 2.2 0 0 0-.44-2.427l-.08-.08A2.667 2.667 0 0 1 8 4.938a2.667 2.667 0 0 1 1.887.782l.08.08a2.2 2.2 0 0 0 2.426.44h.107a2.2 2.2 0 0 0 1.333-2.013V4a2.667 2.667 0 1 1 5.334 0v.12A2.2 2.2 0 0 0 20.5 6.133a2.2 2.2 0 0 0 2.427-.44l.08-.08a2.668 2.668 0 1 1 3.773 3.774l-.08.08a2.201 2.201 0 0 0-.44 2.426V12a2.2 2.2 0 0 0 2.013 1.333h.227a2.667 2.667 0 1 1 0 5.334h-.12A2.2 2.2 0 0 0 26.367 20Z"
    />
  </Svg>
)
export default SettingIcon
