import * as React from "react"
import Svg, { SvgProps, G, Path, Defs, ClipPath } from "react-native-svg"
const AmericaFlagIcon = ({ width = 32, height = 32, ...props}: SvgProps & { width?: number; height?: number }) => (
   <Svg
    width={width}
    height={height}
    viewBox="0 0 32 32"
    fill="none"
    {...props}
  >
    <G clipPath="url(#a)">
      <Path
        fill="#F0F0F0"
        d="M16.5 32c8.837 0 16-7.163 16-16s-7.163-16-16-16S.5 7.163.5 16s7.163 16 16 16Z"
      />
      <Path
        fill="#D80027"
        d="M15.804 16H32.5c0-1.444-.193-2.843-.551-4.174H15.804V16ZM15.804 7.652h14.348a16.086 16.086 0 0 0-3.692-4.174H15.804v4.174ZM16.5 32c3.765 0 7.227-1.302 9.96-3.478H6.54A15.931 15.931 0 0 0 16.5 32ZM2.848 24.348h27.304a15.907 15.907 0 0 0 1.797-4.174H1.05a15.905 15.905 0 0 0 1.797 4.174Z"
      />
      <Path
        fill="#0052B4"
        d="M7.912 2.499H9.37l-1.357.985.518 1.594-1.356-.985-1.356.985.447-1.377A16.088 16.088 0 0 0 3.163 7.16h.467l-.863.627c-.134.224-.263.452-.387.683l.412 1.27-.769-.56c-.191.405-.366.82-.523 1.242l.454 1.398H3.63l-1.356.986.518 1.594-1.356-.985-.812.59C.542 14.659.5 15.325.5 16h16V0c-3.16 0-6.107.917-8.588 2.499Zm.62 11.901-1.357-.985-1.356.985.518-1.594-1.356-.986h1.676l.518-1.594.518 1.594H9.37l-1.357.986.518 1.594Zm-.519-6.255.518 1.594-1.356-.985-1.356.985.518-1.594-1.356-.985h1.676l.518-1.595.518 1.595H9.37l-1.357.985Zm6.258 6.255-1.357-.985-1.356.985.518-1.594-1.356-.986h1.676l.518-1.594.518 1.594h1.677l-1.357.986.518 1.594Zm-.519-6.255.518 1.594-1.356-.985-1.356.985.518-1.594-1.356-.985h1.676l.518-1.595.518 1.595h1.677l-1.357.985Zm0-4.661.518 1.594-1.356-.985-1.356.985.518-1.594-1.356-.985h1.676l.518-1.595.518 1.595h1.677l-1.357.985Z"
      />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M.5 0h32v32H.5z" />
      </ClipPath>
    </Defs>
  </Svg>
)
export default AmericaFlagIcon
