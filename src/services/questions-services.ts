import { supabase } from '../lib/supabase'
import { Question, QuestionWithOptions } from '@/types/database';

type QuestionsServicesResponse<T> = {
  data: T | null;
  error: string | null;
};

export class QuestionService {
  static async getAllQuestions(): Promise<QuestionsServicesResponse<QuestionWithOptions[]>> {
    try {
      const { data, error } = await supabase
        .from('questions')
        .select(`
          *,
          question_variants:question_variant(
            *,
            options:options(*)
          )
        `)
        .order('created_at', { ascending: false })

      if (error) throw error

      return { data: data || [], error: null }
    } catch (error) {
      console.error('Error fetching questions:', error)
      return { data: null, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  static async addQuestion(payload: Question): Promise<QuestionsServicesResponse<Question>> {
    try {
      const { data, error } = await supabase
        .from('questions')
        .insert([payload])
        .select()
        .single()

      if (error) throw error

      return { data, error: null }
    } catch (error) {
      console.error('Error adding question:', error)
      return { data: null, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  static async updateQuestion(id: string, updates: Partial<Question>): Promise<QuestionsServicesResponse<Question>> {
    try {
      const { data, error } = await supabase
        .from('questions')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error

      return { data, error: null }
    } catch (error) {
      console.error('Error updating question:', error)
      return { data: null, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  static async deleteQuestion(id: string): Promise<QuestionsServicesResponse<boolean>> {
    try {
      const { error } = await supabase
        .from('questions')
        .delete()
        .eq('id', id)

      if (error) throw error

      return { data: true, error: null }
    } catch (error) {
      console.error('Error deleting question:', error)
      return { data: null, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}