import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';

import vi from './locales/vi.json';
import en from './locales/en.json';

import type { Language } from '@/types/i18n';

export const STORAGE_KEY = 'user-language';
export const DEFAULT_LANGUAGE: Language = 'vi';
export const FALLBACK_LANGUAGE: Language = 'en';

const resources = {
  vi: { translation: vi },
  en: { translation: en },
} as const;

// Thêm kiểu cho i18n instance
declare module 'i18next' {
  interface CustomTypeOptions {
    resources: typeof resources['vi'];
  }
}

let isInitializing = false;
let initializationPromise: Promise<void> | null = null;

export const initI18n = async (): Promise<boolean> => {
  // Tránh khởi tạo nhiều lần
  if (i18n.isInitialized) return true;
  if (isInitializing) return initializationPromise!.then(() => true);
  
  isInitializing = true;
  
  try {
    initializationPromise = (async () => {
      let savedLanguage: Language = DEFAULT_LANGUAGE;
      
      try {
        const stored = await AsyncStorage.getItem(STORAGE_KEY);
        if (stored === 'vi' || stored === 'en') {
          savedLanguage = stored as Language;
        }
      } catch (error) {
        console.warn('Failed to load saved language:', error);
      }

      await i18n
        .use(initReactI18next)
        .init({
          resources,
          lng: savedLanguage,
          fallbackLng: FALLBACK_LANGUAGE,
          interpolation: {
            escapeValue: false,
          },
          react: {
            useSuspense: false,
          },
          compatibilityJSON: 'v4', // Quan trọng cho React Native
        });
    })();

    await initializationPromise;
    return true;
  } catch (error) {
    console.error('i18n initialization failed:', error);
    return false;
  } finally {
    isInitializing = false;
  }
};

// Helper function để đảm bảo i18n đã sẵn sàng
export const ensureI18nReady = async () => {
  if (!i18n.isInitialized) {
    await initI18n();
  }
  return i18n;
};

export { i18n as default };