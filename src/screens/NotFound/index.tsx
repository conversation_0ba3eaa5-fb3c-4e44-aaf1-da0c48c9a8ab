import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import type { StackNavigationProp } from '@react-navigation/stack';
import type { RootStackParamList } from '@/types/navigation';

type NotFoundScreenNavigationProp = StackNavigationProp<RootStackParamList, 'NotFound'>;

export default function NotFoundScreen({ navigation } : { navigation: NotFoundScreenNavigationProp }) {
  return (
    <View style={styles.container}>
      <Text>This screen does not exist.</Text>
      <TouchableOpacity 
        style={styles.link} 
        onPress={() => navigation.navigate('Home')}
      >
        <Text>Go to home screen!</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  link: {
    marginTop: 15,
    paddingVertical: 15,
  },
});