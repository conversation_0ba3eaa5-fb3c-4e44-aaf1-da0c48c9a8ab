import Background from '@/assets/images/home_bg.png';
import {
  CardCrossingLines,
  CardEmberAfterglow,
  CardFirstDate,
  CardLateNightTalk,
} from '@/partials';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import React, { useEffect, useRef, useState } from 'react';
import {
  Dimensions,
  ImageBackground,
  InteractionManager,
  Platform,
  Pressable,
  StatusBar,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Rive from 'rive-react-native';
import CarouselCard from '@/src/components/CarouselCard';
import { FONTS } from '@/constants/fonts';
import Card<PERSON>rossingLinesBSContent from '@src/components/CardCrossingLinesBSContent';
import CardFirstDateBSContent from '@src/components/CardFirstDateBSContent';
import CardLateNightTalkBSContent from '@src/components/CardLateNightTalkBSContent';
import CardEmberAfterglowBSContent from '@src/components/CardEmberAfterglowBSContent';
import PreparePlayLayer from '@src/components/PreparePlayLayer';
import {  useNavigation } from '@react-navigation/native';
import type { HomeScreenNavigationProp } from '@/types/navigation';
import { useAppContext } from '@src/context/AppContext';
import REDUCER from '@constants/reducer';
import { useTypedTranslation } from '@src/hooks/useTypedTranslation';
import { QuestionService } from '@src/services/questions-services';

const { height: screenHeight } = Dimensions.get('window');

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export default function HomeScreen() {
  const bottomSheetRef = useRef<BottomSheet>(null);
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const insets = useSafeAreaInsets();
  const [isPreparePlayBSOpen, setIsPreparePlayBSOpen] = useState(false);
  const [playMode, setPlayMode] = useState('single');
  const { dispatch } = useAppContext();

  const { t } = useTypedTranslation();

  const [activeSlide, setActiveSlide] = useState(0);

  const handleOpen = () => {
    bottomSheetRef.current?.expand();
  };

  const handleClose = () => {
    bottomSheetRef.current?.close();
  };

  const onOpenPreparePlayBS = async () => {
    try {
      handleClose();
      setIsPreparePlayBSOpen(true);

      const questionsResponse = await QuestionService.getAllQuestions();

      const questions = questionsResponse.data || [];

      dispatch({
        type: REDUCER.SET_QUESTIONS,
        payload: {
          questions,
        },
      });

      await delay(5000);

      setIsPreparePlayBSOpen(false);
      navigation.navigate('BaseGamePlay');
    } catch (error) {
      console.log('🚀 ~ onOpenPreparePlayBS ~ error:', error);
    }
  };

  const data = [
    { id: 1, comp: CardFirstDate, onPress: handleOpen },
    { id: 2, comp: CardLateNightTalk, onPress: handleOpen },
    { id: 3, comp: CardEmberAfterglow, onPress: handleOpen },
    { id: 4, comp: CardCrossingLines, onPress: handleOpen },
  ];

  const renderBottomSheetContent = () => {
    switch (activeSlide) {
      case 0:
        return (
          <CardFirstDateBSContent
            onClose={handleClose}
            onTryFree={onOpenPreparePlayBS}
          />
        );
      case 1:
        return (
          <CardLateNightTalkBSContent
            onClose={handleClose}
            onTryFree={onOpenPreparePlayBS}
          />
        );
      case 2:
        return (
          <CardEmberAfterglowBSContent
            onClose={handleClose}
            onTryFree={onOpenPreparePlayBS}
          />
        );
      case 3:
        return (
          <CardCrossingLinesBSContent
            onClose={handleClose}
            onTryFree={onOpenPreparePlayBS}
          />
        );
      default:
        return null;
    }
  };

  useEffect(() => {
    const interaction = InteractionManager.runAfterInteractions(() => {
      StatusBar.setBarStyle('light-content', true);
      if (Platform.OS === 'android') {
        StatusBar.setBackgroundColor('transparent', true);
        StatusBar.setTranslucent(true);
      }
    });

    return () => interaction.cancel();
  }, []);

  return (
    <>
      <StatusBar
        barStyle="light-content"
        backgroundColor={Platform.OS === 'android' ? 'transparent' : undefined}
        translucent={true}
        hidden={false}
      />
      <ImageBackground
        source={Background}
        style={[
          styles.container,
          { paddingTop: insets.top, paddingBottom: insets.bottom },
        ]}
      >
        <View style={styles.fireContainer}>
          <Rive
            resourceName="heart_brigde_v2"
            style={{ width: 60, height: 60 }}
            autoplay
          />
        </View>
        <View style={styles.cardContainer}>
          <CarouselCard
            data={data}
            setActiveSlide={activeSlide => {
              setActiveSlide(activeSlide);
            }}
          />
        </View>
        <View style={styles.navigationContainer}>
          <View style={styles.navigationButtonContainer}>
            <Pressable
              onPress={() => setPlayMode('single')}
              style={styles.navigationButton}
            >
              <Text
                style={[
                  styles.navigationText,
                  playMode === 'single' && styles.activeText,
                ]}
              >
                {t('home.single')}
              </Text>
            </Pressable>
            {/* <Pressable
              style={styles.navigationButton}
              onPress={() => setPlayMode('double')}
            >
              <Text
                style={[
                  styles.navigationText,
                  playMode === 'double' && styles.activeText,
                ]}
              >
                Multiple
              </Text>
            </Pressable> */}
          </View>
        </View>
      </ImageBackground>
      <BottomSheet
        ref={bottomSheetRef}
        snapPoints={['93%']}
        backgroundStyle={styles.bsStyle}
        handleComponent={null}
        enableHandlePanningGesture={false}
        enablePanDownToClose={true}
        index={-1}
        enableDynamicSizing={false}
      >
        <BottomSheetView style={styles.bsContentContainer}>
          {renderBottomSheetContent()}
        </BottomSheetView>
      </BottomSheet>
      <PreparePlayLayer visible={isPreparePlayBSOpen} />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0B1713',
    justifyContent: 'space-between',
  },
  cardContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bsContentContainer: {
    flex: 1,
    height: screenHeight * 0.93,
  },
  fireContainer: {
    marginBottom: 100,
    alignItems: 'center',
  },
  image: {
    width: 100,
    height: 100,
  },
  navigationContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  navigationButtonContainer: {
    width: '60%',
    flexDirection: 'row',
    backgroundColor: '#313131B2',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 32,
    borderRadius: 60,
  },
  navigationButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  navigationText: {
    textAlign: 'center',
    fontSize: 16,
    fontFamily: FONTS.BALOO2.REGULAR,
    fontWeight: 500,
    color: '#4A4A4A',
  },
  activeText: {
    color: '#fff',
  },
  bsStyle: {
    backgroundColor: 'transparent',
  },
});
