import React, {
  createContext,
  useReducer,
  useContext,
  Dispatch,
  useEffect,
} from 'react';
import { AppAction, appReducer, initialState } from './AppReducer';
import { AppState } from '@/types/app-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import i18n, { ensureI18nReady, STORAGE_KEY } from '@src/i18n';
import { Language } from '@/types/i18n';
import REDUCER from '@constants/reducer';
import { I18nManager } from 'react-native';

type AppContextType = {
  state: AppState;
  dispatch: Dispatch<AppAction>;
  changeLanguage: (language: Language) => Promise<void>;
};

const AppContext = createContext<AppContextType>({
  state: initialState,
  dispatch: () => null, // Placeholder function
  changeLanguage: () => Promise.resolve(), // Placeholder function
});

export const AppProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  useEffect(() => {
    loadSavedLanguage();
  }, []);

  const loadSavedLanguage = async (): Promise<void> => {
    try {
      if (!i18n) {
        console.error('i18n is not initialized!');
        return;
      }
      const savedLanguage = await AsyncStorage.getItem(STORAGE_KEY);
      if (savedLanguage && (savedLanguage === 'vi' || savedLanguage === 'en')) {
        const language = savedLanguage as Language;
        dispatch({ type: REDUCER.SET_LANGUAGE, payload: language });
        await i18n.changeLanguage(language);
      }
    } catch (error) {
      console.warn('Error loading language:', error);
    } finally {
    }
  };

const changeLanguage = async (language: Language): Promise<void> => {
  try {
    // Đảm bảo i18n đã sẵn sàng
    const i18nInstance = await ensureI18nReady();
    
    // Kiểm tra ngôn ngữ có tồn tại không
    if (!i18nInstance.hasResourceBundle(language, 'translation')) {
      throw new Error(`Language ${language} not available`);
    }

    // Xử lý RTL nếu cần
    const isRTL = ['ar', 'he'].includes(language);
    if (I18nManager.isRTL !== isRTL) {
      I18nManager.allowRTL(isRTL);
      I18nManager.forceRTL(isRTL);
    }

    // Lưu và cập nhật ngôn ngữ
    await AsyncStorage.setItem(STORAGE_KEY, language);
    await i18nInstance.changeLanguage(language);
    dispatch({ type: REDUCER.SET_LANGUAGE, payload: language });
    
  } catch (error) {
    console.error('Language change failed:', error);
    throw error;
  }
};

  return (
    <AppContext.Provider value={{ state, dispatch, changeLanguage }}>
      {children}
    </AppContext.Provider>
  );
};

// Custom hook để truy cập Context
export const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within AppProvider');
  }
  return context;
};
