import 'react-native-url-polyfill/auto'
import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/database'

const supabaseUrl = 'https://exslhvvumjuuypkyiwwm.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV4c2xodnZ1bWp1dXlwa3lpd3dtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQyMzQ2MjAsImV4cCI6MjA2OTgxMDYyMH0.a5ktYvCvCPUYVMJGkXIk0d6FaDPVRmf7sEpnBPRAxXk'

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)