import { FONTS } from '@constants/fonts';
import { BlurView } from '@react-native-community/blur';
import AmericaFlagIcon from '@src/icons/AmericaFlagIcon';
import ArrowRightIcon from '@src/icons/ArrowRightIcon';
import XIcon from '@src/icons/XIcon';
import React, { useRef } from 'react';
import {
  View,
  StyleSheet,
  Text,
  Modal,
  Dimensions,
  Switch,
  Pressable,
} from 'react-native';
import LanguageBS from '../LanguageBS';
import { useAppContext } from '@src/context/AppContext';
import VietnamFlagIcon from '@src/icons/VietnamFlagIcon';
import REDUCER from '@constants/reducer';
import { useTypedTranslation } from '@src/hooks/useTypedTranslation';

const { width, height } = Dimensions.get('window');

const SettingsModal = ({
  visible = false,
  onClose = () => {},
}: {
  visible?: boolean;
  backgroundColor?: string;
  text?: string;
  onClose?: () => void;
}) => {
  const bottomSheetRef = useRef<{
    open: () => void;
    close: () => void;
    snapToIndex: (index: number) => void;
  }>(null);

  const { state, dispatch } = useAppContext();
  const { t } = useTypedTranslation();

  return (
    <Modal
      id="settings-modal"
      transparent={true}
      animationType="fade"
      visible={visible}
      onRequestClose={() => {}}
      style={styles.overlay}
    >
      <BlurView
        blurType="dark"
        blurAmount={20}
        reducedTransparencyFallbackColor="white"
        style={[styles.blurView]}
      >
        <View style={[styles.contentContainer]}>
          <View style={styles.container}>
            <View style={styles.closeButtonContainer}>
              <Pressable onPress={onClose} style={styles.closeButtonContainer}>
                <XIcon width={24} height={24} />
              </Pressable>
            </View>
            <Text style={styles.title}>{t('settings.title')}</Text>
            <View style={styles.labelContainer}>
              <View style={styles.labelItemContainer}>
                <Pressable
                  onPress={() => {
                    bottomSheetRef.current?.open();
                  }}
                >
                  <View style={styles.labelItemContainer}>
                    <Text style={[styles.label]}>{t('settings.language')} </Text>
                    <ArrowRightIcon width={12} height={12} />
                  </View>
                </Pressable>
                <View style={styles.languageContainer}>
                  {state.language === 'en' ? (
                    <AmericaFlagIcon width={24} height={24} />
                  ) : (
                    <VietnamFlagIcon width={24} height={24} />
                  )}
                  <Text style={[styles.label]}>
                    {state.language === 'en' ? 'Eng' : 'Vie'}{' '}
                  </Text>
                </View>
              </View>
              <View style={styles.labelItemContainer}>
                <Text style={styles.label}>{t('settings.sound_effect')}</Text>
                <Switch value={false} />
              </View>
              <View style={styles.labelItemContainer}>
                <View style={styles.labelItem}>
                  <Text style={styles.label}>
                    {t('settings.face_to_face_mode')}
                  </Text>
                  <Text style={styles.labelDesc}>
                    {t('settings.face_to_face_mode_desc')}
                  </Text>
                </View>
                <Switch
                  value={state.faceToFaceMode}
                  onValueChange={() => {
                    dispatch({ type: REDUCER.TOGGLE_FACE_TO_FACE_MODE });
                  }}
                />
              </View>
            </View>
          </View>
        </View>
      </BlurView>
      <LanguageBS ref={bottomSheetRef} />
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    justifyContent: 'center',
    alignItems: 'center',
    width: width,
    height: height,
    position: 'relative',
  },
  closeButtonContainer: {
    position: 'absolute',
    top: 6,
    right: 10,
    padding: 8,
    zIndex: 1,
  },
  container: {
    backgroundColor: '#0B1713B2',
    paddingHorizontal: 24,
    paddingVertical: 32,
    borderRadius: 40,
    position: 'relative',
    gap: 24,
  },
  spinner: {
    marginBottom: 15,
  },
  text: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  blurView: {
    flex: 1,
  },
  backgroundStyle: {
    backgroundColor: 'transparent',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  title: {
    fontSize: 32,
    fontWeight: 600,
    fontFamily: FONTS.CRIMSON_TEXT.REGULAR,
    color: 'rgba(255, 255, 255)',
  },
  labelContainer: {
    gap: 24,
  },
  label: {
    fontSize: 16,
    fontFamily: FONTS.BALOO2.REGULAR,
    color: 'rgba(255, 255, 255, 0.85)',
    lineHeight: 24,
  },
  labelItemContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  languageContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  labelItem: {
    gap: 8,
    width: '80%',
  },
  labelDesc: {
    fontSize: 13,
    fontFamily: FONTS.BALOO2.REGULAR,
    color: '#8D8D8D',
    lineHeight: 16,
  },
});

export default SettingsModal;
