import ArrowLeftIcon from '@src/icons/ArrowLeftIcon';
import HelpIcon from '@src/icons/HelpIcon';
import SettingIcon from '@src/icons/SettingIcon';
import SpotifyIcon from '@src/icons/SpotifyIcon';
import React from 'react';
import { Pressable, StyleSheet, View } from 'react-native';

function ControlButtons({
  onOpenSettings = () => {},
  onGoBack = () => {},
}: {
  onOpenSettings?: () => void;
  onGoBack?: () => void;
}) {
  return (
    <>
      <View style={styles.iconContainer}>
        <Pressable
          onPress={() => {
            onGoBack();
          }}
        >
          <ArrowLeftIcon width={32} height={32}/>
        </Pressable>
        <Pressable
          onPress={() => {
            onOpenSettings();
          }}
        >
          <SettingIcon width={32} height={32} />
        </Pressable>
        <HelpIcon width={32} height={32} onPress={() => {}} />
        <SpotifyIcon width={32} height={32} onPress={() => {}} />
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  iconContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    gap: 40,
  },
});

export default ControlButtons;
