import * as React from 'react';
import { Dimensions, Pressable, StyleSheet, View } from 'react-native';
import Animated, { interpolate, useSharedValue } from 'react-native-reanimated';
import Carousel, { TAnimationStyle } from 'react-native-reanimated-carousel';

interface RenderItemProps {
  item: {
    comp: React.ComponentType;
    onPress: () => void;
  };
  index: number;
  animationValue: any;
}

const { width: screenWidth } = Dimensions.get('window');

const renderItem = () => {
  // eslint-disable-next-line react/display-name
  return ({ item, index }: RenderItemProps) => {
    const Component = item.comp;

    return (
      <Animated.View key={index} style={[styles.cardStyle]}>
        <Pressable onPress={item.onPress}>
          <Component />
        </Pressable>
      </Animated.View>
    );
  };
};

function CarouselCard({
  data,
  setActiveSlide,
  // activeSlide,
}: {
  data: any[];
  setActiveSlide: (index: number) => void;
  activeSlide?: number;
}) {
  const progress = useSharedValue<number>(0);

  const onProgressChange = (offsetProgress: number) => {
    progress.value = offsetProgress;
  };

  const animationStyle: TAnimationStyle = React.useCallback((value: number) => {
    'worklet';

    const zIndex = Math.round(interpolate(value, [-1, 0, 1], [10, 20, 30]));
    const rotateZ = `${Math.round(
      interpolate(value, [-1, 0, 1], [-8, 0, 8]),
    )}deg`;
    const translateX = Math.round(
      interpolate(
        value,
        [-1, 0, 1],
        [-screenWidth * 0.8, 0, screenWidth * 0.8],
      ),
    );
    const translateY = Math.round(interpolate(value, [-1, 0, 1], [15, 0, 15]));

    return {
      transform: [{ translateX }, { translateY }, { rotateZ }] as const,
      zIndex,
    };
  }, []);

  return (
    <View>
      <Carousel
        autoPlayInterval={4000}
        data={data}
        height={400}
        width={screenWidth * 0.8}
        loop={true}
        pagingEnabled={true}
        snapEnabled={true}
        style={styles.carouselStyle}
        mode="parallax"
        modeConfig={{
          parallaxScrollingScale: 0.85,
          parallaxScrollingOffset: 80,
        }}
        onProgressChange={onProgressChange}
        renderItem={renderItem()}
        customAnimation={animationStyle}
        onSnapToItem={index => {
          setActiveSlide(index);
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  carouselStyle: {
    width: screenWidth,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardStyle: {
    width: '100%',
    height: '100%',
    borderRadius: 20,
    padding: 20,
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 15,
  },
});

export default CarouselCard;
