import React, {
  use<PERSON>allback,
  useMemo,
  useRef,
  useState,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import BottomSheet, {
  BottomSheetView,
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
} from '@gorhom/bottom-sheet';
import { BlurView } from '@react-native-community/blur';
import { FONTS } from '@constants/fonts';
import AmericaFlagIcon from '@src/icons/AmericaFlagIcon';
import VietnamFlagIcon from '@src/icons/VietnamFlagIcon';
import CheckIcon from '@src/icons/CheckIcon';
import { useAppContext } from '@src/context/AppContext';
import { useTypedTranslation } from '@src/hooks/useTypedTranslation';
import { Language } from '@/types/i18n';

// Types
interface CardData {
  id: number;
  title: string;
  content: string;
}

interface BottomSheetItem {
  id: number;
  title: string;
  description: string;
}

type BlurType = 'light' | 'dark' | 'xlight' | 'prominent';

interface BlurBottomSheetProps {
  initialSnapIndex?: number;
  snapPoints?: string[];
  blurType?: BlurType;
  blurAmount?: number;
}

interface BlurBottomSheetRef {
  open: () => void;
  close: () => void;
  snapToIndex: (index: number) => void;
}

const LanguageBS = forwardRef<BlurBottomSheetRef, BlurBottomSheetProps>(
  (
    {
      initialSnapIndex = -1,
      snapPoints: customSnapPoints,
      blurType = 'dark',
      blurAmount = 10,
    },
    ref,
  ) => {
    const bottomSheetRef = useRef<BottomSheet>(null);
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const { state, changeLanguage } = useAppContext();

    const { t } = useTypedTranslation();

    const handleLanguageChange = async (language: Language): Promise<void> => {
      if (language !== state.language) {
        await changeLanguage(language);
      }
    };

    // Snap points cho bottom sheet
    const snapPoints = useMemo(
      () => customSnapPoints || ['25%'],
      [customSnapPoints],
    );

    // Expose methods to parent component
    useImperativeHandle(
      ref,
      () => ({
        open: () => {
          bottomSheetRef.current?.expand();
          setIsOpen(true);
        },
        close: () => {
          bottomSheetRef.current?.close();
          setIsOpen(false);
        },
        snapToIndex: (index: number) => {
          bottomSheetRef.current?.snapToIndex(index);
        },
      }),
      [],
    );

    // Hàm đóng bottom sheet
    const handleCloseBottomSheet = useCallback((): void => {
      bottomSheetRef.current?.close();
      setIsOpen(false);
    }, []);

    // Handle sheet changes
    const handleSheetChanges = useCallback((index: number): void => {
      setIsOpen(index >= 0);
    }, []);

    // Custom backdrop với blur effect
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const renderBackdrop = useCallback(
      (props: BottomSheetBackdropProps) => (
        <BottomSheetBackdrop
          {...props}
          disappearsOnIndex={-1}
          appearsOnIndex={0}
          opacity={0.5}
          pressBehavior="close"
        />
      ),
      [],
    );

    // Custom backdrop với BlurView (iOS)
    const renderBlurBackdrop = useCallback(
      (props: BottomSheetBackdropProps) => {
        if (isOpen) {
          return (
            <View style={[StyleSheet.absoluteFillObject]}>
              <BlurView
                style={StyleSheet.absoluteFillObject}
                blurType={blurType}
                blurAmount={blurAmount}
                reducedTransparencyFallbackColor="rgba(0,0,0,0.5)"
              />
            </View>
          );
        }

        // Fallback cho Android
        return (
          <BottomSheetBackdrop
            {...props}
            disappearsOnIndex={-1}
            appearsOnIndex={0}
            opacity={0.7}
            pressBehavior="close"
          />
        );
      },
      [isOpen, blurType, blurAmount],
    );

    return (
      <>
        <BottomSheet
          ref={bottomSheetRef}
          index={initialSnapIndex}
          snapPoints={snapPoints}
          backdropComponent={renderBlurBackdrop}
          enablePanDownToClose={true}
          onChange={handleSheetChanges}
          backgroundStyle={styles.bottomSheetBackground}
          handleIndicatorStyle={styles.handleIndicator}
          animateOnMount={true}
        >
          <BottomSheetView style={styles.bottomSheetContent}>
            <Text style={styles.bottomSheetTitle}>{t('settings.language')}</Text>
            <View style={styles.scrollContent}>
              <TouchableOpacity
                onPress={() => {
                  handleLanguageChange('en');
                  handleCloseBottomSheet();
                }}
              >
                <View style={styles.bottomSheetItem}>
                  <AmericaFlagIcon width={32} height={32} />
                  <Text style={[styles.itemTitle]}>English </Text>
                  {state.language === 'en' && <CheckIcon />}
                </View>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  handleLanguageChange('vi');
                  handleCloseBottomSheet();
                }}
              >
                <View style={styles.bottomSheetItem}>
                  <VietnamFlagIcon width={32} height={32} />
                  <Text style={[styles.itemTitle]}>Vietnamese </Text>
                  {state.language === 'vi' && <CheckIcon />}
                </View>
              </TouchableOpacity>
            </View>
          </BottomSheetView>
        </BottomSheet>
      </>
    );
  },
);

LanguageBS.displayName = 'BlurBottomSheetExample';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0B1713B2',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  // Bottom sheet styles
  bottomSheetBackground: {
    backgroundColor: '#0B1713B2',
    borderRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  handleIndicator: {
    backgroundColor: '#E0E0E0',
    width: 40,
    height: 4,
  },
  bottomSheetContent: {
    flex: 1,
    paddingHorizontal: 24,
  },
  bottomSheetTitle: {
    fontSize: 32,
    marginBottom: 20,
    color: '#FFFFFF',
    fontFamily: FONTS.CRIMSON_TEXT.SEMI_BOLD,
  },

  // Close button
  closeButton: {
    backgroundColor: '#FF3B30',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  closeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },

  // Scroll content
  scrollContent: {
    gap: 10,
  },

  // Bottom sheet items
  bottomSheetItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 10,
  },
  itemTitle: {
    fontSize: 16,
    color: 'white',
    fontFamily: FONTS.CRIMSON_TEXT.REGULAR,
  },
  itemDescription: {
    fontSize: 14,
    color: '#666',
  },

  // Blur overlay
  blurOverlay: {
    zIndex: 0,
    pointerEvents: 'none',
  },
  androidBlurOverlay: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    zIndex: 1,
    pointerEvents: 'none',
  },
});

export default LanguageBS;

// Export types for reuse
export type { BlurBottomSheetProps, CardData, BottomSheetItem, BlurType };
