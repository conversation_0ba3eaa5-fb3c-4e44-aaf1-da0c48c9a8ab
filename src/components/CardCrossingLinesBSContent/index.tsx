import React from 'react';
import { FONTS } from '@constants/fonts';
import Cover from '@/assets/images/bg_crossing_lines.png';
import Lock from '@/assets/images/lock.png';
import Clock from '@/assets/images/clock.png';
import MoneyFly from '@/assets/images/money_fly.png';
import Replay from '@/assets/images/replay.png';
import EarPhone from '@/assets/images/ear_phone.png';
import { View, Text, StyleSheet, Image } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import CardButton from '@/partials/card-button';
import CardShadowButton from '@/partials/card-shadow-button';
import XIcon from '@src/icons/XIcon';
import { BlurView } from '@react-native-community/blur';

// Props của BottomSheetComponent
interface BottomSheetProps {
  onClose?: () => void;
  onTryFree?: () => void;
  onRent?: () => void;
}

// Sử dụng forwardRef để t<PERSON><PERSON>ền ref từ cha vào
const CardCrossingLinesBSContent = ({
  onClose,
  onTryFree = () => {},
  onRent = () => {},
}: BottomSheetProps) => {
  const insets = useSafeAreaInsets();

  return (
    <BlurView
      blurType="light"
      blurAmount={20}
      reducedTransparencyFallbackColor="white"
      style={styles.blurView}
    >
      <LinearGradient
        colors={[
          'rgba(195, 102, 138, 0.7)',
          'rgba(162, 218, 253, 0.7)',
          'rgba(35, 172, 250, 0.7)',
          'rgba(15, 19, 129, 0.7)',
        ]}
        locations={[0, 0.07, 0.17, 0.45]}
        style={styles.linearContainer}
      >
        <View style={[styles.container, { paddingBottom: insets.bottom }]}>
          <View style={styles.closeButtonContainer}>
            <XIcon width={24} height={24} onPress={onClose} />
          </View>
          <View style={styles.contentContainer}>
            <View style={styles.imageContainer}>
              <Image source={Cover} resizeMode="cover" style={styles.image} />
            </View>
            <View>
              <Text style={styles.title}>Crossing Lines</Text>
              <View style={styles.descriptionContainer}>
                <Text style={styles.description}>
                  When friendship starts feeling like more.
                </Text>
              </View>
            </View>
            <View style={styles.infoContainer}>
              <View style={styles.infoItem}>
                <Image
                  source={Lock}
                  resizeMode="cover"
                  style={styles.contentIcon}
                />
                <Text style={styles.infoLabel}>
                  Get all 100 thoughtful questions
                </Text>
              </View>
              <View style={styles.infoItem}>
                <Image
                  source={Clock}
                  resizeMode="cover"
                  style={styles.contentIcon}
                />
                <Text style={styles.infoLabel}>
                  No subscription needed - Rent for 4 hours
                </Text>
              </View>
              <View style={styles.infoItem}>
                <Image
                  source={MoneyFly}
                  resizeMode="cover"
                  style={styles.contentIcon}
                />
                <Text style={styles.infoLabel}>
                  No auto-renew - pay only when you want
                </Text>
              </View>
              <View style={styles.infoItem}>
                <Image
                  source={EarPhone}
                  resizeMode="cover"
                  style={styles.contentIcon}
                />
                <Text style={styles.infoLabel}>No Ads music background</Text>
              </View>
              <View style={styles.infoItem}>
                <Image
                  source={Replay}
                  resizeMode="cover"
                  style={styles.contentIcon}
                />
                <Text style={styles.infoLabel}>Fresh content every year</Text>
              </View>
            </View>
            <View style={styles.buttonContainer}>
              <CardShadowButton
                title="Try 10 cards for free"
                onPress={onTryFree}
              />
              <CardButton title="Rent - 4 Hours Access" onPress={onRent} />
            </View>
          </View>

          <View style={styles.termsContainer}>
            <Text style={styles.termsLabel}>Terms of use</Text>
            <Text style={styles.termsLabel}>Privacy Policy</Text>
          </View>
        </View>
      </LinearGradient>
    </BlurView>
  );
};

const styles = StyleSheet.create({
  blurView: {
    flex: 1,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  linearContainer: {
    flex: 1,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  contentContainer: {
    padding: 16,
    gap: 30,
    alignItems: 'stretch',
    width: '100%',
  },
  closeButtonContainer: {
    width: '100%',
    alignItems: 'flex-end',
    padding: 16,
  },
  button: {
    backgroundColor: '#2196F3',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 10,
  },
  closeButton: {
    backgroundColor: '#FF5252',
    marginTop: 20,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  title: {
    fontSize: 40,
    fontFamily: FONTS.IMBUE.BOLD,
    color: 'rgba(255, 255, 255, 0.85)',
    textTransform: 'uppercase',
    textAlign: 'center',
  },
  descriptionContainer: {
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    fontFamily: FONTS.BALOO2.REGULAR,
    color: 'rgba(255, 255, 255, 0.85)',
    opacity: 0.7,
    textAlign: 'center',
  },
  imageContainer: {
    alignItems: 'center',
  },
  image: {
    aspectRatio: 14 / 10,
    width: '90%',
    height: 150,
  },
  infoLabel: {
    fontSize: 16,
    fontFamily: FONTS.BALOO2.REGULAR,
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.85)',
  },
  freeTrial: {
    fontSize: 24,
    fontFamily: FONTS.IMBUE.MEDIUM,
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.85)',
  },
  termsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    textAlign: 'center',
    gap: 67,
  },
  termsLabel: {
    fontSize: 14,
    fontFamily: FONTS.BALOO2.REGULAR,
    color: '#8E7BA1',
    lineHeight: 20,
  },
  buttonContainer: {
    width: '100%',
    gap: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  contentIcon: {
    width: 16,
    height: 16,
    marginRight: 16,
  },
  infoContainer: {
    gap: 8,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default CardCrossingLinesBSContent;
