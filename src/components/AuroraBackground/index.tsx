import React from 'react';
import { View, StyleSheet } from 'react-native';
import { BlurView } from '@react-native-community/blur';


const AuroraBackground = ({ children }: { children: React.ReactNode }) => {
  return (
    <View style={styles.container}>
      {/* Gradient circles */}
      <View style={styles.circleContainer}>
        <View style={[styles.circle, styles.circle1]}></View>
        <View style={[styles.circle, styles.circle2]}></View>
        <BlurView
          blurType="dark"
          blurAmount={30}
          reducedTransparencyFallbackColor="white"
          style={StyleSheet.absoluteFill}
        />
      </View>
      <View style={styles.content}>{children}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#09090b',
    position: 'relative',
    overflow: 'hidden',
  },
  circleContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  circle: {
    position: 'absolute',
    borderRadius: 9999,
    overflow: 'hidden',
  },
  circle1: {
    aspectRatio: 1,
    width: 320.278,
    height: 519.671,
    position: 'absolute',
    top: 39.71,
    left: '-20%',
    transform: [{ rotate: '-34.27deg' }],
    borderRadius: 519.671 / 2, // Sử dụng nửa chiều cao để tạo hình ellipse
    opacity: 1,
    backgroundColor: '#401269',
    zIndex: -2,
  },
  circle2: {
    width: 149.899,
    height: 232.061,
    position: 'absolute',
    top: 550,
    left: '40%',
    transform: [{ rotate: '-34.27deg' }],
    borderRadius: 232.061 / 2, // Tạo hình ellipse
    backgroundColor: '#B16A23',
    zIndex: -3,
  },
  content: {
    flex: 1,
    zIndex: 10,
  },
});

export default AuroraBackground;
