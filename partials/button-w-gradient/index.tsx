import React from 'react';
import { Text, TouchableOpacity, View, StyleSheet } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

interface ButtonProps {
  title: string;
  onPress: () => void;
  size?: number;
  borderWidth?: number;
  textStyle?: any;
  disabled?: boolean;
}

const GradientBorderButton = ({
  title,
  onPress,
  size = 100,
  borderWidth = 3,
  textStyle,
  disabled = false,
}: ButtonProps) => {
  return (
    <View style={[styles.container, { width: size, height: size }]}>
      <View
        style={[styles.gradientBorder, { borderRadius: size / 2 }]}
      >
        <LinearGradient
          colors={['#993074', '#FFAC4E']}
          locations={[0, 1]}
          style={[styles.innerButtonWithGradient, { borderRadius: size / 2 }]}
        >
          <TouchableOpacity
            style={[
              styles.innerButton,
              {
                width: size - borderWidth * 2,
                height: size - borderWidth * 2,
                borderRadius: (size - borderWidth * 2) / 2,
              },
            ]}
            onPress={onPress}
            disabled={disabled}
            activeOpacity={0.8}
          >
            <Text style={[styles.buttonText, textStyle]}>{title}</Text>
          </TouchableOpacity>
        </LinearGradient>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  gradientBorder: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  innerButton: {
    backgroundColor: '#1A042E',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 2,
  },
  innerButtonWithGradient: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  touchableArea: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  largeText: {
    fontSize: 32,
    fontWeight: '600',
  },
  appContainer: {
    flex: 1,
  },
  background: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonsContainer: {
    flexDirection: 'column',
    gap: 20,
    marginBottom: 40,
  },
  simpleButtonsContainer: {
    flexDirection: 'row',
    gap: 20,
  },
});

export default GradientBorderButton;
