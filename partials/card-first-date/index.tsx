import { FONTS } from '@constants/fonts';
import { StyleSheet, Text, View, Image } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Cover from '@/assets/images/bg_first_date.png';

export const CardFirstDate = () => {
  return (
    <LinearGradient
      colors={[
        'rgba(255, 172, 78, 1)',
        'rgba(153, 48, 116, 1)',
        'rgba(64, 0, 64, 1)',
        'rgba(41, 5, 72, 1)',
      ]}
      locations={[0, 0.14, 0.35, 0.55]}
      style={styles.container}
    >
      <View style={styles.contentContainer}>
        <View>
          <Image source={Cover} resizeMode="cover" style={styles.image} />
          <Text style={styles.title}>N+1 date</Text>
          <View style={styles.descriptionContainer}>
            <Text style={styles.description}>Skip the small talk.</Text>
            <Text style={styles.description}>Start something real.</Text>
          </View>
        </View>
        <View style={styles.priceContainer}>
          <Text style={styles.price}>$9.9/ 4h</Text>
          <Text style={styles.freeTrial}>Free trial</Text>
        </View>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    aspectRatio: 7 / 10,
    borderWidth: 1,
    borderColor: '#44077A',
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 28,
    width: 266,
    height: 374,
  },
  imageContainer: {
    aspectRatio: 14 / 10,
    width: 100,
  },
  title: {
    fontSize: 40,
    fontFamily: FONTS.IMBUE.BOLD,
    color: 'rgba(255, 255, 255, 0.85)',
    textTransform: 'uppercase',
    textAlign: 'center',
  },
  descriptionContainer: {
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    fontFamily: FONTS.BALOO2.REGULAR,
    color: 'rgba(255, 255, 255, 0.85)',
    opacity: 0.7,
    textAlign: 'center',
  },
  image: {
    aspectRatio: 14 / 10,
    width: '90%',
    marginLeft: 16,
    height: 150,
  },
  priceContainer: {
    gap: 12,
  },
  price: {
    fontSize: 14,
    fontFamily: FONTS.BALOO2.REGULAR,
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.85)',
  },
  freeTrial: {
    fontSize: 24,
    fontFamily: FONTS.IMBUE.MEDIUM,
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.85)',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
});
