import React from 'react';
import {
  Text,
  StyleSheet,
  View,
  Pressable,
  StyleProp,
  ViewStyle,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

interface ButtonProps {
  title?: string;
  onPress: () => void;
  containerStyle?: StyleProp<ViewStyle>;
  children?: React.ReactNode;
}

const CardShadowButton = ({
  title,
  onPress,
  containerStyle,
  children,
}: ButtonProps) => {
  return (
    <Pressable onPress={onPress} style={[styles.container, containerStyle]}>
      <View style={styles.container}>
        <LinearGradient
          colors={[
            'rgba(91, 0, 127, 1)',
            'rgba(211, 0, 110, 1)',
          ]}
          locations={[0, 1]}
          style={styles.shadowContainer}
        >
        <View style={styles.shadowContainer}>
          <View style={styles.button}>
            {title && <Text style={styles.buttonText}>{title}</Text>}
            {children}
          </View>
        </View>
        </LinearGradient> 
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    maxWidth: 220,
    height: 48,
    maxHeight: 48,
    minWidth: 100,
    overflow: 'hidden',
  },
  shadowContainer: {
    borderRadius: 60,
    margin: 1,
    flex: 1,
  },
  button: {
    flex: 1,
    backgroundColor: '#1B1B1B',
    borderRadius: 60,
    paddingHorizontal: 12,
    paddingVertical: 9,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    color: '#F5F5F5',
    fontWeight: 400,
    lineHeight: 16,
    fontSize: 16,
    textAlign: 'center',
  },
});

export default CardShadowButton;
