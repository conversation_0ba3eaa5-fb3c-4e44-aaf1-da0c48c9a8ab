import { FONTS } from '@constants/fonts';
import { StyleSheet, Text, View, Image } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Cover from '@/assets/images/bg_ember_afterglow.png';

export const CardEmberAfterglow = () => {
  return (
    <LinearGradient
      colors={[
        'rgba(189, 231, 243, 1)',
        'rgba(254, 201, 167, 1)',
        'rgba(251, 154, 189, 1)',
        'rgba(133, 7, 52, 1)',
      ]}
      locations={[0, 0.09, 0.16, 0.45]}
      style={styles.container}
    >
      <View style={styles.contentContainer}>
        <Image source={Cover} resizeMode="cover" style={styles.image} />
        <View style={styles.textContainer}>
          <Text style={styles.title}>EMBER AFTERGLOW</Text>
          <View style={styles.descriptionContainer}>
            <Text style={styles.description}>
              Love doesn't die, it simmers.
            </Text>
          </View>
        </View>
        <View style={styles.priceContainer}>
          <Text style={styles.price}>$9.9/ 4h</Text>
          <Text style={styles.freeTrial}>Free trial</Text>
        </View>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    aspectRatio: 7 / 10,
    borderWidth: 1,
    borderColor: '#800043',
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 28,
    width: 266,
    height: 374,
    position: 'relative',
  },
  imageContainer: {
    aspectRatio: 14 / 10,
    width: 100,
  },
  title: {
    fontSize: 40,
    fontFamily: FONTS.IMBUE.BOLD,
    color: 'rgba(255, 255, 255, 0.85)',
    textTransform: 'uppercase',
    textAlign: 'center',
  },
  descriptionContainer: {
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    fontFamily: FONTS.BALOO2.REGULAR,
    color: 'rgba(255, 255, 255, 0.85)',
    opacity: 0.7,
    textAlign: 'center',
  },
  image: {
    aspectRatio: 14 / 10,
    width: '90%',
    marginLeft: 16,
    height: 150,
  },
  priceContainer: {
    gap: 12,
  },
  price: {
    fontSize: 14,
    fontFamily: FONTS.BALOO2.REGULAR,
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.85)',
  },
  freeTrial: {
    fontSize: 24,
    fontFamily: FONTS.IMBUE.MEDIUM,
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.85)',
  },
  textContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
});
