// import_questions.js
// const { createClient } = require('@supabase/supabase-js')
// const fs = require('fs/promises')
// const { v4: uuidv4 } = require('uuid')

import { createClient } from '@supabase/supabase-js'
import fs from 'fs/promises'
import { v4 as uuidv4 } from 'uuid'


const supabaseUrl = 'https://exslhvvumjuuypkyiwwm.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV4c2xodnZ1bWp1dXlwa3lpd3dtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQyMzQ2MjAsImV4cCI6MjA2OTgxMDYyMH0.a5ktYvCvCPUYVMJGkXIk0d6FaDPVRmf7sEpnBPRAxXk' // cần quyền insert
const supabase = createClient(supabaseUrl, supabaseKey)

async function loadJsonData() {
  try {
    const vietnameseData = JSON.parse(await fs.readFile('./data/vietnamese_data.json', 'utf8'))
    const englishData = JSON.parse(await fs.readFile('./data/english_data.json', 'utf8'))
    
    console.log(`✅ Loaded ${vietnameseData.data.length} Vietnamese questions`)
    console.log(`✅ Loaded ${englishData.data.length} English questions`)
    
    return { vietnameseData, englishData }
  } catch (error) {
    console.error('❌ Error loading JSON files:', error)
    throw error
  }
}

// ======================= TẠO TOPIC MẶC ĐỊNH =======================
async function createDefaultTopic() {
  console.log('\n🚀 Creating default topic...')
  
  const topicData = {
    name_en: 'Dating Questions',
    name_vi: 'Câu Hỏi Hẹn Hò'
  }
  
  const { data, error } = await supabase
    .from('topics')
    .insert([topicData])
    .select()
    .single()
  
  if (error) {
    console.error('❌ Error creating topic:', error)
    throw error
  }
  
  console.log(`✅ Created topic: ${data.name_vi} (ID: ${data.id})`)
  return data.id
}

// ======================= TẠO CÂU HỎI =======================
async function createQuestions(vietnameseData, englishData) {
  console.log('\n🚀 Creating questions...')
  
  const questions = []
  
  // Kết hợp dữ liệu từ 2 file JSON
  for (let i = 0; i < vietnameseData.data.length; i++) {
    const viQuestion = vietnameseData.data[i]
    const enQuestion = englishData.data[i]
    
    // Kiểm tra ID khớp nhau
    if (viQuestion.id !== enQuestion.id) {
      console.warn(`⚠️ ID mismatch at index ${i}: VI=${viQuestion.id}, EN=${enQuestion.id}`)
    }
    
    const questionData = {
      question_vi: viQuestion.question,
      question_en: enQuestion.question,
      is_active: true,
      example: viQuestion.example || null // Một số câu có example
    }
    
    questions.push(questionData)
  }
  
  // Insert tất cả questions vào database
  const { data, error } = await supabase
    .from('questions')
    .insert(questions)
    .select()
  
  if (error) {
    console.error('❌ Error creating questions:', error)
    throw error
  }
  
  console.log(`✅ Created ${data.length} questions`)
  return data
}

// ======================= LIÊN KẾT TOPIC VÀ QUESTIONS =======================
async function linkTopicQuestions(topicId, questions) {
  console.log('\n🚀 Linking topic with questions...')
  
  const topicQuestionLinks = questions.map(question => ({
    topic_id: topicId,
    question_id: question.id
  }))
  
  const { data, error } = await supabase
    .from('topics_questions')
    .insert(topicQuestionLinks)
    .select()
  
  if (error) {
    console.error('❌ Error linking topics and questions:', error)
    throw error
  }
  
  console.log(`✅ Linked ${data.length} topic-question relationships`)
  return data
}

// ======================= TẠO QUESTION VARIANTS VÀ OPTIONS =======================
async function createVariantsAndOptions(vietnameseData, englishData, questions) {
  console.log('\n🚀 Creating question variants and options...')
  
  let variantCount = 0
  let optionCount = 0
  
  for (let i = 0; i < vietnameseData.data.length; i++) {
    const viQuestion = vietnameseData.data[i]
    const enQuestion = englishData.data[i]
    const dbQuestion = questions[i]
    
    // Kiểm tra xem câu hỏi có options không
    if (viQuestion.options || enQuestion.options) {
      console.log(`📝 Processing question ${viQuestion.id} with options...`)
      
      // Tạo question variant
      const variantData = {
        name: 'multiple_choice',
        question_id: dbQuestion.id
      }
      
      const { data: variant, error: variantError } = await supabase
        .from('question_variant')
        .insert([variantData])
        .select()
        .single()
      
      if (variantError) {
        console.error(`❌ Error creating variant for question ${viQuestion.id}:`, variantError)
        continue
      }
      
      variantCount++
      
      // Tạo options cho variant này
      const viOptions = viQuestion.options || []
      const enOptions = enQuestion.options || []
      
      const optionsData = []
      
      // Lấy số lượng options tối đa giữa 2 ngôn ngữ
      const maxOptions = Math.max(viOptions.length, enOptions.length)
      
      for (let j = 0; j < maxOptions; j++) {
        const optionData = {
          text_vi: viOptions[j] || viOptions[0] || 'N/A', // Fallback nếu thiếu
          text_en: enOptions[j] || enOptions[0] || 'N/A',
          is_correct: false, // Có thể điều chỉnh logic này
          question_variant_id: variant.id
        }
        
        optionsData.push(optionData)
      }
      
      const { data: options, error: optionsError } = await supabase
        .from('options')
        .insert(optionsData)
        .select()
      
      if (optionsError) {
        console.error(`❌ Error creating options for question ${viQuestion.id}:`, optionsError)
        continue
      }
      
      optionCount += options.length
      console.log(`  ✅ Created variant with ${options.length} options`)
    } else {
      // Câu hỏi không có options - tạo variant dạng open-ended
      const variantData = {
        name: 'open_ended',
        question_id: dbQuestion.id
      }
      
      const { error: variantError } = await supabase
        .from('question_variant')
        .insert([variantData])
      
      if (!variantError) {
        variantCount++
      }
    }
  }
  
  console.log(`✅ Created ${variantCount} question variants`)
  console.log(`✅ Created ${optionCount} options`)
}

// ======================= MAIN FUNCTION =======================
async function main() {
  try {
    console.log('🎯 Starting data import to Supabase...')
    console.log('=' .repeat(50))
    
    // 1. Load dữ liệu từ file JSON
    const { vietnameseData, englishData } = await loadJsonData()
    
    // 2. Tạo topic mặc định
    const topicId = await createDefaultTopic()
    
    // 3. Tạo questions
    const questions = await createQuestions(vietnameseData, englishData)
    
    // 4. Liên kết topic với questions
    await linkTopicQuestions(topicId, questions)
    
    // 5. Tạo variants và options
    await createVariantsAndOptions(vietnameseData, englishData, questions)
    
    console.log('\n' + '=' .repeat(50))
    console.log('🎉 Data import completed successfully!')
    console.log('📊 Summary:')
    console.log(`   - Topics: 1`)
    console.log(`   - Questions: ${questions.length}`)
    console.log(`   - Topic-Question links: ${questions.length}`)
    
  } catch (error) {
    console.error('\n❌ Import failed:', error.message)
    process.exit(1)
  }
}

// ======================= UTILITY FUNCTIONS =======================

// Function để kiểm tra kết nối Supabase
async function testSupabaseConnection() {
  try {
    const { data, error } = await supabase
      .from('topics')
      .select('count(*)')
      .limit(1)
    
    if (error) throw error
    console.log('✅ Supabase connection successful')
    return true
  } catch (error) {
    console.error('❌ Supabase connection failed:', error)
    return false
  }
}

// Function để xóa tất cả data (để test lại)
async function clearAllData() {
  console.log('🗑️ Clearing all data...')
  
  const tables = ['options', 'question_variant', 'topics_questions', 'questions', 'topics']
  
  for (const table of tables) {
    const { error } = await supabase.from(table).delete().neq('id', '')
    if (error) {
      console.error(`❌ Error clearing ${table}:`, error)
    } else {
      console.log(`✅ Cleared ${table}`)
    }
  }
}

await main()