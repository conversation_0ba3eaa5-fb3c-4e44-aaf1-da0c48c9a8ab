{"language": "english", "data": [{"id": 1, "question": "Let's each describe our ideal day to each other!"}, {"id": 2, "question": "Weekends are for relaxing — what's one thing you really don't want your partner to do on a weekend?"}, {"id": 3, "question": "Is there any food or drink you absolutely can't go a day without?"}, {"id": 4, "question": "Do you have a cozy little corner or favorite spot you'd love to show your partner?"}, {"id": 5, "question": "Tell me one nighttime habit you'd like your partner to do with you."}, {"id": 6, "question": "When you're in the mood to chill, do you prefer reading, watching movies, gaming, listening to music, or scrolling social media? Let's share our favorites!"}, {"id": 7, "question": "Share a good habit you've been trying to stick to recently — would you invite your partner to join you?"}, {"id": 8, "question": "Do you have any quirky habits you'd want your partner to know about? Why do you think they're fun?"}, {"id": 9, "question": "When you're stressed, what do you usually do to decompress? And what would you like your partner to do to help you feel better?"}, {"id": 10, "question": "Share something you're proud of — in love or life. Let's open up together!"}, {"id": 11, "question": "When you're feeling down, what kind of comfort do you want most from your partner?"}, {"id": 12, "question": "When you need to make a quick decision, do you rely on your gut or want your partner to help you sort things out?"}, {"id": 13, "question": "Let's share how we each reset our mood when we're drained — and what we hope our partner would do in those moments."}, {"id": 14, "question": "If you had free time with no deadlines or work stress, would you rather be alone or with your partner? And what would you like to do together?"}, {"id": 15, "question": "Would you say you're more introverted, extroverted, or somewhere in between? Can you share a story that shows that?"}, {"id": 16, "question": "If your partner isn't exactly your 'ideal type,' would you be open to adjusting or adapting?"}, {"id": 17, "question": "The more we love, the more our real selves show — and that might not always match how things started. How do you keep the spark alive when that happens?"}, {"id": 18, "question": "Do you have any habits that help you feel balanced again when your mood is off? How do you want your partner to support you?"}, {"id": 19, "question": "Pick one of these that makes you feel most seen and appreciated after you've put in effort for your partner — and share why you chose it: ", "options": ["A. A hug or kiss with a sweet compliment", "B. Being taken out for a special meal", "C. A public shout-out on social media", "<PERSON><PERSON> Quiet recognition and a surprise reminder later on."]}, {"id": 20, "question": "What are some things that remind you of your childhood — and why are they special to you?"}, {"id": 21, "question": "Has anyone in your family influenced how you show care in a relationship?"}, {"id": 22, "question": "Who gave you the most meaningful lesson about love, life, or learning — and what was it?"}, {"id": 23, "question": "If your family has a special tradition, what would you most want your partner to be part of?"}, {"id": 24, "question": "Is there anything from your family that you'd want to carry into your future family?"}, {"id": 25, "question": "Share something you wished for as a kid — that you've done or would love to do with your partner now."}, {"id": 26, "question": "What's something an elder in your family once taught you that still holds true today?"}, {"id": 27, "question": "If you brought your partner home to meet your family, what would make you most nervous or excited?"}, {"id": 28, "question": "In your view, what should both families have in common to make the relationship easier and more joyful? (Culture, region, education, finances, personality, traditions, religion...)"}, {"id": 29, "question": "If you both went back to your hometowns, where would you want to take your partner first?"}, {"id": 30, "question": "What would your ideal relationship look like if the two of you built it together?"}, {"id": 31, "question": "In a relationship, what makes you feel most secure and trusted?"}, {"id": 32, "question": "What do you think made your partner decide to give this relationship a real shot?"}, {"id": 33, "question": "When you're in love, what helps you feel most like yourself?"}, {"id": 34, "question": "Let's rank these by importance in a relationship: being affectionate, sharing the little things, being emotionally attentive, having humor, keeping promises, avoiding misunderstandings."}, {"id": 35, "question": "If you had to change or adjust a habit for your partner, would you be willing to? Why?"}, {"id": 36, "question": "If you moved in together, what would you need to prepare to avoid disappointment?"}, {"id": 37, "question": "What's your view on having personal space in a relationship? Where should the boundaries be?"}, {"id": 38, "question": "When there's a fight or disagreement, how do you prefer to make up? Share an example if you have one."}, {"id": 39, "question": "Is there a habit you'd love to turn into a sweet couple ritual? Like a good morning kiss, checking in before going out, or saying goodnight every night."}, {"id": 40, "question": "When you're telling a story, how do you want your partner to react? ", "options": ["A. Listen and support, even if it's a bit irrational", "B. Listen and give advice if you're wrong", "<PERSON>. Just listen — that's enough", "<PERSON><PERSON> Something else?"]}, {"id": 41, "question": "Try looking into each other's eyes for 5 seconds — then share your first impression at this moment."}, {"id": 42, "question": "Choose one word that describes the kind of love you want: `Smoldering,` `Passionate,` `Peaceful,` `Warm,` or `Steady` — and explain why."}, {"id": 43, "question": "When do you prefer to go public with a relationship?", "options": ["<PERSON><PERSON> officially confessing", "B. While still getting to know each other", "<PERSON><PERSON> After telling both families"]}, {"id": 44, "question": "List 3 things that made you feel like you `matched` with your partner since the first conversation."}, {"id": 45, "question": "Take turns sharing two things you absolutely want to do together once you're officially a couple."}, {"id": 46, "question": "Take 60 seconds to think — then say out loud any words or situations you consider off-limits in love."}, {"id": 47, "question": "Name two small things you'd love your partner to do every day — even when you're busy, apart, or it's a rainy day."}, {"id": 48, "question": "Choose two of the following and share why: `When I'm with my partner, I like…`", "options": ["A. Being myself without pressure — no need to dress up or wear perfume.", "B. Being spoiled, acting a little silly, and teasing them.", "C. Being more put-together because I don't want to look messy or smelly.", "D. Being taken care of — work is hard, and I just want to feel seen and loved."]}, {"id": 49, "question": "Take turns picking 3–5 things that make your partner feel secure in love: no opposite-gender best friends, no `em trai/mưa` vibes, couple profile pics, nicknames, clearly stating you're taken, etc."}, {"id": 50, "question": "Each of you pick a little secret you've been keeping — and try revealing it when you feel ready to trust."}, {"id": 51, "question": "When you're stressed, do you prefer to open up first — or wait for your partner to ask?"}, {"id": 52, "question": "If you both liked something a little weird, would you be open to sharing it with your partner publicly?"}, {"id": 53, "question": "Let's create a new couple tradition together."}, {"id": 54, "question": "Each of you share one unspoken rule about healthy jealousy that you want your partner to understand."}, {"id": 55, "question": "If there's something corny but sweet you want to try with your partner but feel shy about saying it, how would you let them know?"}, {"id": 56, "question": "In love, are you the one who says sorry first — or do you wait for your partner to reach out?"}, {"id": 57, "question": "If you both accidentally forgot your anniversary, who would you jokingly blame — and how would you fix it?"}, {"id": 58, "question": "If you wanted to take things to the next level, what would need to change to make the relationship stronger?"}, {"id": 59, "question": "Imagine your first trip away together — what experience would you want to share most?"}, {"id": 60, "question": "If you had to be apart for a month, what would be the most important thing to keep the love alive?"}, {"id": 61, "question": "If you two got even closer, what kind of couple nickname would you want? (E.g.: `The Bear House,` `<PERSON> <PERSON><PERSON>,` `<PERSON> Shorties,` etc.)"}, {"id": 62, "question": "Each of you share your idea of a perfect date."}, {"id": 63, "question": "When you think about the future with your partner, what excites you most? (E.g.: planning a big trip, moving in together, getting a pet, sharing a workspace...)"}, {"id": 64, "question": "If you had to do long distance for a while, what rules would you set together?"}, {"id": 65, "question": "Share one slightly crazy thing you want to do with your partner in the first year of dating. (E.g.: book a spontaneous trip after one week, take dance classes together, start a podcast about your love story...)"}, {"id": 66, "question": "If you had a couple's bucket list, what would be the first thing on it?"}, {"id": 67, "question": "If you imagined your wedding day, what would matter most to you?"}, {"id": 68, "question": "Pick one: Create a couple playlist together — or cook something totally new for the first time?"}, {"id": 69, "question": "Think back: what was the first affectionate gesture from your partner that made your heart skip a beat?"}, {"id": 70, "question": "On your first hangout with your partner's best friends, do you prefer to dive in or observe quietly?"}, {"id": 71, "question": "If you only had one day left together, what would you want to do most?"}, {"id": 72, "question": "If you had to choose, do you prefer a talkative or a quiet partner?"}, {"id": 73, "question": "Each of you share a funny or embarrassing moment from when you first started dating."}, {"id": 74, "question": "Challenge: In 2 minutes, brainstorm a surprise gift idea for your partner. Write it down — then go make it happen!"}, {"id": 75, "question": "If both of you are busy, how would you make sure you don't grow distant?"}, {"id": 76, "question": "If you spent a day exploring the city together, where would you most want to take your partner?"}, {"id": 77, "question": "What's one small thing you think would make your partner feel more loved each day?"}], "total_records": 77, "created_at": "2025-07-26T10:03:28.948Z"}