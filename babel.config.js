module.exports = {
  presets: ['module:@react-native/babel-preset'],
  plugins: [
     [
      'module-resolver',
      {
        root: ['./'],
        extensions: ['.js', '.jsx', '.ts', '.tsx', '.json'],
        alias: {
          '@constants': './constants',
          '@assets': './assets',
          '@src': './src',
          '@': './',
        },
      },
    ],
    'react-native-reanimated/plugin', // <-- Must be listed last
  ],
};
