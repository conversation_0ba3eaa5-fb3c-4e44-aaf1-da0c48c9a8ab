<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>loveDeck</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string/>
	<key>RCTNewArchEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>UIAppFonts</key>
	<array>
		<string>Baloo2-Bold.ttf</string>
		<string>Baloo2-ExtraBold.ttf</string>
		<string>Baloo2-Medium.ttf</string>
		<string>Baloo2-Regular.ttf</string>
		<string>Baloo2-SemiBold.ttf</string>
		<string>Imbue-400Regular.ttf</string>
		<string>Imbue-500Medium.ttf</string>
		<string>Imbue-600SemiBold.ttf</string>
		<string>Imbue-700Bold.ttf</string>
		<string>Imbue-800ExtraBold.ttf</string>
		<string>Imbue-900Black.ttf</string>
		<string>CrimsonText-Bold.ttf</string>
		<string>CrimsonText-BoldItalic.ttf</string>
		<string>CrimsonText-Italic.ttf</string>
		<string>CrimsonText-Regular.ttf</string>
		<string>CrimsonText-SemiBold.ttf</string>
		<string>CrimsonText-SemiBoldItalic.ttf</string>
	</array>
</dict>
</plist>
