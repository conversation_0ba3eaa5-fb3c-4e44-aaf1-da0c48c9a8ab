{"name": "loveDeck", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@expo-google-fonts/baloo-2": "^0.4.1", "@expo-google-fonts/imbue": "^0.4.1", "@gorhom/bottom-sheet": "^5.1.6", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/blur": "^4.4.1", "@react-native/new-app-screen": "0.80.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/elements": "^2.5.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@supabase/supabase-js": "^2.53.0", "expo-font": "^13.3.2", "i18next": "^25.3.2", "install": "^0.13.0", "npm": "^11.4.2", "react": "19.1.0", "react-i18next": "^15.6.1", "react-native": "0.80.1", "react-native-gesture-handler": "^2.27.1", "react-native-linear-gradient": "^2.8.3", "react-native-reanimated": "^3.18.0", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "^5.5.1", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0", "react-native-url-polyfill": "^2.0.0", "rive-react-native": "^9.4.0", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.0.0", "@react-native-community/cli-platform-android": "19.0.0", "@react-native-community/cli-platform-ios": "19.0.0", "@react-native/babel-preset": "0.80.1", "@react-native/eslint-config": "0.80.1", "@react-native/metro-config": "0.80.1", "@react-native/typescript-config": "0.80.1", "@types/i18next": "^13.0.0", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}