buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "27.1.12297006"
        kotlinVersion = "1.8.0"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.3.1") // Thêm version
        classpath("com.facebook.react:react-native-gradle-plugin:0.74.1") // Thêm version
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion") // Dùng biến
    }
}

allprojects {
    repositories {
        maven {
            // Thêm nếu cần các repository đặc biệt
            url("$rootDir/../node_modules/detox/Detox-android")
        }
        google()
        mavenCentral()
    }
}

apply plugin: "com.facebook.react.rootproject"
